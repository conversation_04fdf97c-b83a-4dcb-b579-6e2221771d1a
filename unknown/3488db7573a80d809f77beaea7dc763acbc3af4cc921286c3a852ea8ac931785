import { Metadata } from "next";
import { siteConfig, getUrl } from "@/config/site";

// Define metadata for the home page
export const metadata: Metadata = {
  title: `${siteConfig.name} | Premium Wooden Furniture from Pakistan`,
  description: `Discover authentic ${
    siteConfig.name
  } furniture, handcrafted with ${siteConfig.slogan.toLowerCase()}. Shop our premium collection of beds, tables, chairs, and more.`,
  keywords: siteConfig.seo.keywords,
  openGraph: {
    type: "website",
    url: getUrl(),
    title: `${siteConfig.name} | Premium Wooden Furniture from Pakistan`,
    description: `Discover authentic ${
      siteConfig.name
    } furniture, handcrafted with ${siteConfig.slogan.toLowerCase()}. Shop our premium collection of beds, tables, chairs, and more.`,
    images: [
      {
        url: getUrl("/og-image.jpg"),
        width: 1200,
        height: 630,
        alt: `${siteConfig.name} - Premium Furniture`,
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: `${siteConfig.name} | Premium Wooden Furniture`,
    description: `Discover authentic ${
      siteConfig.name
    } furniture, handcrafted with ${siteConfig.slogan.toLowerCase()}.`,
    images: [getUrl("/twitter-image.jpg")],
  },
  alternates: {
    canonical: getUrl(),
  },
};
