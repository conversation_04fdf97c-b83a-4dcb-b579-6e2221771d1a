"use client";
import Hero from "@/sections/Hero";
import ProductsSection from "@/sections/ProductsSection";
import { motion } from "framer-motion";
import OrganizationJsonLd from "@/components/SEO/OrganizationJsonLd";
import LocalBusinessJsonLd from "@/components/SEO/LocalBusinessJsonLd";
import { metadata } from "./metadata";

const Home = () => {
  // Base URL for the site
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://chiniotiart.com";

  return (
    <>
      {/* SEO Structured Data */}
      <OrganizationJsonLd url={baseUrl} />
      <LocalBusinessJsonLd url={baseUrl} />

      <Hero />

      {/* Featured Products */}
      <ProductsSection
        title="Featured Products"
        subtitle="Discover our handpicked selection of exceptional Chinioti wooden furniture pieces."
        filter="featured"
      />

      {/* New Arrivals */}
      <ProductsSection
        title="New Arrivals"
        subtitle="The latest additions to our collection of premium Chinioti craftsmanship."
        filter="new"
        limit={4}
      />

      {/* Trending Products */}
      <ProductsSection
        title="Trending Now"
        subtitle="Our most popular Chinioti wooden furniture pieces that customers love."
        filter="trending"
        limit={8}
      />
    </>
  );
};

export default Home;
