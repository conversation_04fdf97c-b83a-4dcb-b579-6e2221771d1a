"use client";
import React from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Award, Users, Heart, Truck } from "lucide-react";

const AboutUs = () => {
  const features = [
    {
      icon: <Award className="h-8 w-8 text-accent" />,
      title: "Premium Quality",
      description: "Handcrafted furniture made from the finest wood with traditional Chinioti techniques."
    },
    {
      icon: <Users className="h-8 w-8 text-accent" />,
      title: "Expert Craftsmen",
      description: "Our skilled artisans have generations of experience in creating beautiful wooden furniture."
    },
    {
      icon: <Heart className="h-8 w-8 text-accent" />,
      title: "Made with Love",
      description: "Every piece is crafted with passion and attention to detail, ensuring lasting beauty."
    },
    {
      icon: <Truck className="h-8 w-8 text-accent" />,
      title: "Worldwide Delivery",
      description: "We deliver our exquisite furniture pieces to customers around the globe."
    }
  ];

  const stats = [
    { number: "25+", label: "Years of Experience" },
    { number: "1000+", label: "Happy Customers" },
    { number: "500+", label: "Unique Designs" },
    { number: "50+", label: "Countries Served" }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              About <span className="text-accent">Chinioti Wooden Art</span>
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-8">
              Preserving the rich heritage of Chinioti craftsmanship while creating timeless furniture pieces that bring elegance and warmth to your home.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Our Story</h2>
              <p className="text-gray-600 mb-6">
                Founded in the heart of Chiniot, Pakistan, our company has been dedicated to preserving and promoting the ancient art of wooden furniture making. Chiniot has been renowned for its exquisite woodwork for centuries, and we are proud to continue this legacy.
              </p>
              <p className="text-gray-600 mb-6">
                Our master craftsmen use traditional techniques passed down through generations, combined with modern design sensibilities to create furniture that is both timeless and contemporary. Each piece tells a story of dedication, skill, and passion for the craft.
              </p>
              <p className="text-gray-600">
                From intricate carved details to smooth finishes, every aspect of our furniture reflects the rich cultural heritage of Chiniot and the commitment to excellence that defines our brand.
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="relative"
            >
              <Image
                src="/assets/backgrounds/home-bg.png"
                alt="Chinioti craftsman at work"
                width={600}
                height={400}
                className="rounded-lg shadow-lg"
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Why Choose Us</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We combine traditional craftsmanship with modern quality standards to deliver exceptional furniture that stands the test of time.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="text-center h-full hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <div className="flex justify-center mb-4">
                      {feature.icon}
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-4xl md:text-5xl font-bold text-accent mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 bg-accent text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Our Mission</h2>
            <p className="text-lg md:text-xl opacity-90">
              To preserve and promote the traditional art of Chinioti woodwork while creating beautiful, functional furniture that enhances homes around the world. We are committed to sustainable practices, fair trade, and supporting our local artisan community.
            </p>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default AboutUs;
