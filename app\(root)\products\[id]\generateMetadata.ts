import { Metadata } from "next";
import { getProductById } from "@/apis/products";
import { siteConfig, getUrl } from "@/config/site";

// Generate metadata for product pages
export async function generateMetadata({
  params,
}: {
  params: { id: string };
}): Promise<Metadata> {
  try {
    // Fetch product data
    const product = await getProductById(params.id);

    if (!product) {
      return {
        title: "Product Not Found",
        description: "The requested product could not be found.",
      };
    }

    // Calculate discounted price if available
    const discountedPrice =
      product.discount && product.discount !== "0"
        ? Math.round(
            product.price - product.price * (parseInt(product.discount) / 100)
          )
        : null;

    // Format price for display
    const priceDisplay = discountedPrice
      ? `$${discountedPrice} (${product.discount}% off $${product.price})`
      : `$${product.price}`;

    // Determine product image URL
    const productImage =
      product.images && product.images.length > 0
        ? product.images[0]
        : product.image || getUrl("/og-image.jpg");

    // Ensure image URL is absolute
    const imageUrl = productImage.startsWith("http")
      ? productImage
      : getUrl(productImage);

    // Generate metadata
    return {
      title: product.title || product.name,
      description:
        product.description ||
        `${product.title || product.name} - ${
          product.category
        } furniture from ${siteConfig.name}. ${priceDisplay}. ${
          product.available ? "In stock" : "Out of stock"
        }.`,
      keywords: [
        product.title || product.name,
        product.category,
        ...siteConfig.seo.keywords,
      ],
      openGraph: {
        title: product.title || product.name,
        description:
          product.description ||
          `${product.title || product.name} - ${
            product.category
          } furniture from ${siteConfig.name}. ${priceDisplay}.`,
        url: getUrl(`/products/${params.id}`),
        siteName: siteConfig.name,
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: product.title || product.name,
          },
        ],
        locale: "en_US",
        type: "product",
      },
      twitter: {
        card: "summary_large_image",
        title: product.title || product.name,
        description:
          product.description ||
          `${product.title || product.name} - ${
            product.category
          } furniture from ${siteConfig.name}. ${priceDisplay}.`,
        images: [imageUrl],
      },
      alternates: {
        canonical: getUrl(`/products/${params.id}`),
      },
    };
  } catch (error) {
    console.error("Error generating product metadata:", error);
    return {
      title: `Product - ${siteConfig.name}`,
      description: `Explore our premium ${siteConfig.name} collection.`,
    };
  }
}
