'use client';
import React from 'react';
import { HiOutlineShoppingBag } from 'react-icons/hi2';
import { motion } from 'framer-motion';
import { useCart } from '@/contexts/CartContext';
import Link from 'next/link';

interface CartIconProps {
  className?: string;
}

const CartIcon: React.FC<CartIconProps> = ({ className = '' }) => {
  const { cart } = useCart();

  const iconVariants = {
    hover: {
      scale: 1.2,
      color: 'var(--accent)',
      transition: { type: 'spring', stiffness: 400, damping: 10 },
    },
  };

  return (
    <Link href="/cart">
      <motion.div
        className={`relative cursor-pointer p-1.5 sm:p-2 hover:bg-accent/10 rounded-full transition-colors min-w-[40px] min-h-[40px] flex items-center justify-center ${className}`}
        whileHover="hover"
        variants={iconVariants}
      >
        <HiOutlineShoppingBag size={18} className="sm:w-5 sm:h-5 md:w-[22px] md:h-[22px]" />

        {cart.totalItems > 0 && (
          <motion.div
            className="absolute -top-0.5 -right-0.5 sm:-top-1 sm:-right-1 bg-accent text-white text-xs font-bold rounded-full w-4 h-4 sm:w-5 sm:h-5 flex items-center justify-center shadow-sm text-[10px] sm:text-xs"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: 'spring', stiffness: 500, damping: 15 }}
          >
            {cart.totalItems > 9 ? "9+" : cart.totalItems}
          </motion.div>
        )}
      </motion.div>
    </Link>
  );
};

export default CartIcon;
