'use client';

import React from 'react';
import { useCurrency } from '@/contexts/CurrencyContext';
import { CurrencyCode } from '@/constants/helpers';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown, DollarSign } from 'lucide-react';

interface CurrencySelectorProps {
  className?: string;
  variant?: 'default' | 'compact';
}

const CurrencySelector: React.FC<CurrencySelectorProps> = ({ 
  className = '', 
  variant = 'default' 
}) => {
  const { selectedCurrency, setCurrency, getAllCurrencies } = useCurrency();
  const currencies = getAllCurrencies();

  const handleCurrencyChange = (currency: CurrencyCode) => {
    setCurrency(currency);
  };

  if (variant === 'compact') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={`h-8 sm:h-8 px-1.5 sm:px-2 text-xs min-w-[40px] flex items-center justify-center ${className}`}
          >
            <DollarSign size={12} className="sm:w-3.5 sm:h-3.5 mr-0.5 sm:mr-1" />
            <span className="hidden xs:inline">{currencies[selectedCurrency].code}</span>
            <span className="xs:hidden">{currencies[selectedCurrency].symbol}</span>
            <ChevronDown size={10} className="sm:w-3 sm:h-3 ml-0.5 sm:ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-28 sm:w-32 space-y-1 p-1">
          {Object.entries(currencies).map(([code, currency]) => (
            <DropdownMenuItem
              key={code}
              onClick={() => handleCurrencyChange(code as CurrencyCode)}
              className={`cursor-pointer mb-1 last:mb-0 ${
                selectedCurrency === code ? 'bg-accent text-white' : ''
              }`}
            >
              <span className="text-xs">
                {currency.symbol} {currency.code}
              </span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className={`flex items-center gap-4 ${className}`}
        >
          <DollarSign size={16} />
          <span>{currencies[selectedCurrency].code}</span>
          <span className="text-sm text-gray-500">
            ({currencies[selectedCurrency].symbol})
          </span>
          <ChevronDown size={16} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48 space-y-1 p-1">
        {Object.entries(currencies).map(([code, currency]) => (
          <DropdownMenuItem
            key={code}
            onClick={() => handleCurrencyChange(code as CurrencyCode)}
            className={`cursor-pointer flex items-center gap-2 justify-between mb-1 last:mb-0 ${
              selectedCurrency === code ? 'bg-accent' : ''
            }`}
          >
            <div className="flex items-center gap-4">
              <span className="font-medium">{currency.symbol}</span>
              <span>{currency.code}</span>
            </div>
            <span className="text-sm text-gray-500">{currency.name}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default CurrencySelector;
