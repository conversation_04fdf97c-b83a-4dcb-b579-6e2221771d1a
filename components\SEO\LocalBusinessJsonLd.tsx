"use client";

import <PERSON>rip<PERSON> from "next/script";
import { siteConfig } from "@/config/site";

interface LocalBusinessJsonLdProps {
  url: string;
}

/**
 * Generates JSON-LD structured data for a local business
 * This helps search engines understand the business information
 * and can enhance search results with rich snippets
 */
const LocalBusinessJsonLd = ({ url }: LocalBusinessJsonLdProps) => {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "FurnitureStore",
    name: siteConfig.name,
    image: `${url}/og-image.jpg`,
    "@id": `${url}/#store`,
    url: url,
    telephone: siteConfig.contact.phone,
    priceRange: "$$",
    address: {
      "@type": "PostalAddress",
      streetAddress: siteConfig.contact.address.street,
      addressLocality: siteConfig.contact.address.city,
      addressRegion: siteConfig.contact.address.region,
      postalCode: siteConfig.contact.address.postalCode,
      addressCountry: siteConfig.contact.address.country.substring(0, 2),
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: siteConfig.seo.location.latitude,
      longitude: siteConfig.seo.location.longitude,
    },
    openingHoursSpecification: [
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: [
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
        ],
        opens: siteConfig.seo.businessHours.weekdays.open,
        closes: siteConfig.seo.businessHours.weekdays.close,
      },
      {
        "@type": "OpeningHoursSpecification",
        dayOfWeek: "Sunday",
        opens: siteConfig.seo.businessHours.sunday.open,
        closes: siteConfig.seo.businessHours.sunday.close,
      },
    ],
    sameAs: [
      siteConfig.social.facebook,
      siteConfig.social.instagram,
      siteConfig.social.twitter,
    ],
  };

  return (
    <Script id="local-business-jsonld" type="application/ld+json">
      {JSON.stringify(jsonLd)}
    </Script>
  );
};

export default LocalBusinessJsonLd;
