'use client';

import Script from 'next/script';

interface BreadcrumbItem {
  name: string;
  url: string;
}

interface BreadcrumbJsonLdProps {
  items: BreadcrumbItem[];
  baseUrl: string;
}

/**
 * Generates JSON-LD structured data for breadcrumbs
 * This helps search engines understand the page hierarchy
 * and can enhance search results with breadcrumb navigation
 */
const BreadcrumbJsonLd = ({ items, baseUrl }: BreadcrumbJsonLdProps) => {
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url.startsWith('http') ? item.url : `${baseUrl}${item.url}`,
    })),
  };

  return (
    <Script id="breadcrumb-jsonld" type="application/ld+json">
      {JSON.stringify(jsonLd)}
    </Script>
  );
};

export default BreadcrumbJsonLd;
