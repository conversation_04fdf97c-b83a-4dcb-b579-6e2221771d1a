"use client";
import React, { useState, useRef, useEffect } from "react";
import { HiOutlineUser } from "react-icons/hi2";
import { IoHeartOutline } from "react-icons/io5";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import { Button } from "./ui/button";
import CartIcon from "./cart/CartIcon";
import CurrencySelector from "./CurrencySelector";
import { useAuth } from "@/contexts/AuthContext";
import { useWishlist } from "@/contexts/WishlistContext";

const ToolBar = () => {
  const [showAuthMenu, setShowAuthMenu] = useState(false);
  const { user, isAuthenticated, isLoading, logout } = useAuth();
  const { wishlist } = useWishlist();
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: Event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowAuthMenu(false);
      }
    };

    if (showAuthMenu) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [showAuthMenu]);

  const iconVariants = {
    hover: {
      scale: 1.2,
      color: "var(--accent)",
      transition: { type: "spring", stiffness: 400, damping: 10 },
    },
  };

  return (
    <div className="flex flex-row items-center text-foreground gap-1 sm:gap-2 md:gap-3 flex-shrink-0">
      <div className="relative overflow-visible" ref={dropdownRef}>
        <motion.div
          className="relative cursor-pointer p-1.5 z-10 hover:bg-accent/10 rounded-full transition-colors min-w-[40px] min-h-[40px] flex items-center justify-center"
          whileHover="hover"
          variants={iconVariants}
          onClick={() => setShowAuthMenu(!showAuthMenu)}
        >
          <HiOutlineUser size={18} className="w-[18px] h-[18px] sm:w-5 sm:h-5" />
        </motion.div>

        {/* Auth dropdown menu */}
        <AnimatePresence>
          {showAuthMenu && (
            <motion.div
              className="fixed sm:absolute left-1/2 sm:right-0 sm:left-auto top-16 sm:top-auto mt-0 sm:mt-2 w-72 sm:w-56 md:w-48 bg-white rounded-lg shadow-xl py-2 z-50 border border-gray-200 transform -translate-x-1/2 sm:translate-x-0 max-w-[calc(100vw-1rem)] sm:max-w-none"
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              style={{
                boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
              }}
            >
              <div className="px-4 py-2 border-b border-gray-100">
                <h3 className="text-sm font-medium text-gray-700">
                  {isLoading
                    ? "Loading..."
                    : isAuthenticated
                    ? `Hello, ${user?.name?.split(" ")[0]}`
                    : "Account"}
                </h3>
              </div>

              {isLoading ? (
                <div className="px-4 py-4 text-sm text-gray-500 flex items-center justify-center">
                  <div className="h-4 w-4 border-2 border-gray-300 border-t-accent rounded-full animate-spin mr-2"></div>
                  <span>Authenticating...</span>
                </div>
              ) : !isAuthenticated ? (
                <>
                  <Link href="/sign-in" className="block" onClick={() => setShowAuthMenu(false)}>
                    <motion.div
                      className="px-4 py-2 text-sm hover:bg-accent/10 transition-colors flex items-center gap-2"
                      whileHover={{ x: 5 }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-gray-500"
                      >
                        <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4" />
                        <polyline points="10 17 15 12 10 7" />
                        <line x1="15" y1="12" x2="3" y2="12" />
                      </svg>
                      <span>Sign In</span>
                    </motion.div>
                  </Link>

                  <Link href="/sign-up" className="block" onClick={() => setShowAuthMenu(false)}>
                    <motion.div
                      className="px-4 py-2 text-sm hover:bg-accent/10 transition-colors flex items-center gap-2"
                      whileHover={{ x: 5 }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-gray-500"
                      >
                        <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
                        <circle cx="8.5" cy="7" r="4" />
                        <line x1="20" y1="8" x2="20" y2="14" />
                        <line x1="23" y1="11" x2="17" y2="11" />
                      </svg>
                      <span>Sign Up</span>
                    </motion.div>
                  </Link>
                </>
              ) : (
                <>
                  <Link href="/profile" className="block" onClick={() => setShowAuthMenu(false)}>
                    <motion.div
                      className="px-4 py-2 text-sm hover:bg-accent/10 transition-colors flex items-center gap-2"
                      whileHover={{ x: 5 }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-gray-500"
                      >
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                        <circle cx="12" cy="7" r="4" />
                      </svg>
                      <span>My Profile</span>
                    </motion.div>
                  </Link>

                  <motion.div
                    className="px-4 py-2 text-sm hover:bg-accent/10 transition-colors flex items-center gap-2 cursor-pointer"
                    whileHover={{ x: 5 }}
                    onClick={() => {
                      logout();
                      setShowAuthMenu(false);
                    }}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-gray-500"
                    >
                      <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                      <polyline points="16 17 21 12 16 7" />
                      <line x1="21" y1="12" x2="9" y2="12" />
                    </svg>
                    <span>Sign Out</span>
                  </motion.div>
                </>
              )}

              {!isLoading && (
                <div className="border-t border-gray-100 my-1"></div>
              )}

              {!isLoading && (
                <>
                  <Link href="/orders" className="block" onClick={() => setShowAuthMenu(false)}>
                    <motion.div
                      className="px-4 py-2 text-sm hover:bg-accent/10 transition-colors flex items-center gap-2"
                      whileHover={{ x: 5 }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-gray-500"
                      >
                        <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" />
                      </svg>
                      <span>My Orders</span>
                    </motion.div>
                  </Link>

                  <Link href="/wishlist" className="block" onClick={() => setShowAuthMenu(false)}>
                    <motion.div
                      className="px-4 py-2 text-sm hover:bg-accent/10 transition-colors flex items-center gap-2"
                      whileHover={{ x: 5 }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-gray-500"
                      >
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
                      </svg>
                      <span>My Wishlist</span>
                      {wishlist.length > 0 && (
                        <span className="ml-auto bg-accent text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                          {wishlist.length > 9 ? "9+" : wishlist.length}
                        </span>
                      )}
                    </motion.div>
                  </Link>
                </>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <Link href="/wishlist">
        <motion.div
          className="relative cursor-pointer p-1.5 hover:bg-accent/10 rounded-full transition-colors min-w-[40px] min-h-[40px] flex items-center justify-center"
          whileHover="hover"
          variants={iconVariants}
        >
          <IoHeartOutline size={18} className="w-[18px] h-[18px] sm:w-5 sm:h-5" />
          {wishlist.length > 0 && (
            <span className="absolute -top-0.5 -right-0.5 bg-accent text-white text-xs font-bold rounded-full h-4 w-4 sm:h-5 sm:w-5 flex items-center justify-center text-[10px] sm:text-xs">
              {wishlist.length > 9 ? "9+" : wishlist.length}
            </span>
          )}
        </motion.div>
      </Link>

      <CartIcon />

      {/* Currency Selector */}
      <div className="hidden sm:block">
        <CurrencySelector variant="compact" />
      </div>

      {/* Direct Sign In/Sign Up buttons for desktop only */}
      <div className="hidden lg:flex items-center gap-2">
        {isLoading ? (
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 border-2 border-gray-300 border-t-accent rounded-full animate-spin"></div>
            <span className="text-xs text-gray-500">Loading...</span>
          </div>
        ) : !isAuthenticated ? (
          <>
            <Link href="/sign-in">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs hover:bg-accent/10 hover:text-accent"
              >
                Sign In
              </Button>
            </Link>
            <Link href="/sign-up">
              <Button
                variant="default"
                size="sm"
                className="text-xs bg-accent hover:bg-accent/90"
              >
                Sign Up
              </Button>
            </Link>
          </>
        ) : (
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">
              Hello, {user?.name?.split(" ")[0]}
            </span>
            <Button
              variant="ghost"
              size="sm"
              className="text-xs hover:bg-accent/10 hover:text-accent"
              onClick={logout}
            >
              Sign Out
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ToolBar;
