"use client";
import React, { useState, useEffect, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { getProducts } from "@/apis/products";
import ProductCard from "@/components/ProductCard";
import SearchField from "@/components/SearchField";
import useDebounce from "@/hooks/useDebounce";

// Loading component for Products page
const ProductsLoading = () => {
  return (
    <div className="container mx-auto px-4 py-16 flex justify-center items-center">
      <div className="animate-pulse">Loading products...</div>
    </div>
  );
};

const Products = () => {
  return (
    <Suspense fallback={<ProductsLoading />}>
      <ProductsContent />
    </Suspense>
  );
};

const ProductsContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryParam = searchParams.get("query");

  // State for products and filtering
  const [products, setProducts] = useState<ProductData[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<ProductData[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>(queryParam || "");
  const [isLoading, setIsLoading] = useState(true);

  // Debounce search query
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Load products
  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        const allProducts = await getProducts();
        setProducts(allProducts);
        setFilteredProducts(allProducts);
      } catch (error) {
        console.error("Error fetching products:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Apply search filter when debouncedSearchQuery changes
  useEffect(() => {
    if (products.length > 0) {
      let filtered = [...products];

      // Apply search filter
      if (debouncedSearchQuery) {
        const query = debouncedSearchQuery.toLowerCase();
        filtered = filtered.filter(
          (product) =>
            product.title.toLowerCase().includes(query) ||
            product.category.toLowerCase().includes(query) ||
            (product.description &&
              product.description.toLowerCase().includes(query))
        );
      }

      setFilteredProducts(filtered);
    }
  }, [debouncedSearchQuery, products]);

  // Update search query when URL parameter changes
  useEffect(() => {
    if (queryParam) {
      setSearchQuery(queryParam);
    }
  }, [queryParam]);

  // Handle search submission
  const handleSearch = (query: string) => {
    setSearchQuery(query);

    // Update URL with search query
    const params = new URLSearchParams();
    if (query) {
      params.append("query", query);
    }
    const url = params.toString() ? `/products?${params.toString()}` : "/products";
    router.push(url);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-16 flex justify-center items-center">
        <div className="animate-pulse">Loading products...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Our Products</h1>
        <p className="text-gray-600">
          Discover our collection of handcrafted Chinioti wooden furniture
        </p>
      </div>

      {/* Search bar */}
      <div className="mb-6">
        <SearchField
          fullWidth
          placeholder="Search for products..."
          onSearch={handleSearch}
          initialQuery={searchQuery}
          debounceTime={300}
          autoSubmit={true}
          products={products}
          showSuggestions={true}
          maxSuggestions={5}
          onSelectSuggestion={(product) => {
            router.push(`/products/${product.id}`);
          }}
        />

        {/* Search results info */}
        {searchQuery && (
          <div className="mt-3 text-sm text-gray-600">
            <p>
              Showing results for{" "}
              <span className="font-medium">&quot;{searchQuery}&quot;</span>
            </p>
          </div>
        )}
      </div>

      {/* Products grid */}
      {filteredProducts.length === 0 ? (
        <div className="text-center py-16">
          <h3 className="text-xl font-medium mb-2">No products found</h3>
          <p className="text-gray-500">
            Try adjusting your search or check back later.
          </p>
        </div>
      ) : (
        <>
          <div className="flex justify-between items-center mb-6">
            <p className="text-gray-600">
              {searchQuery
                ? `Found ${filteredProducts.length} ${
                    filteredProducts.length === 1 ? "result" : "results"
                  }`
                : `Showing ${filteredProducts.length} ${
                    filteredProducts.length === 1 ? "product" : "products"
                  }`}
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProducts.map((product, idx) => (
              <ProductCard key={product.id} product={product} index={idx} />
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default Products;
