import React from "react";
import { But<PERSON> } from "../components/ui/button";
import { secondaryFont } from "@/constants/fonts";

const Hero = () => {
  return (
    <div
      className="bg-cover bg-no-repeat bg-center w-full h-[30rem] px-5 "
      style={{
        backgroundImage: "url('/assets/backgrounds/home-bg.png')",
      }}
    >
      <div className="flex flex-col justify-center items-start h-full gap-10 backdrop-brightness-60">
        <div
          className={
            secondaryFont +
            "text-5xl font-semibold font-secondary text-background leading-15"
          }
        >
          Discover the World of <span className="text-accent ">Chinioti</span>{" "}
          <br /> Craftsmanship and Design
        </div>
        <Button color="dark">{"Discover More >"}</Button>
      </div>
    </div>
  );
};

export default Hero;
